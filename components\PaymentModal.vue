<template>
  <component
    :is="isDesktop ? Modal : Slideover"
    side="bottom"
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="สแกนเพื่อโอนเงิน"
  >
    <template #body>
      <div v-if="newPromptPayId">
        <div
          v-if="qrCodeDataUrl"
          class="item-center flex flex-col justify-center"
        >
          <img
            src="/qr-head.png"
          />
          <img
            :src="qrCodeDataUrl"
            alt="PromptPay QR Code"
            class="mx-auto w-64"
          />
        </div>
        <div class="mb-2 flex flex-col text-center">
          <p class="mb-2 font-semibold">
            ผู้รับ
          </p>
          <PersonAvatar
            :person="recipient"
          />
        </div>
        <p class="mb-1 text-center text-xs text-neutral-500">
          รหัสพร้อมเพย์: {{ newPromptPayId }}
        </p>
        <p class="mb-4 text-center text-xl">
          จำนวน: <strong>{{ NumberHelper.toCurrency(transfer.amount) }} ฿</strong>
        </p>

        <div class="flex justify-center gap-3">
          <Button
            :href="qrCodeDataUrl"
            :download="`promptpay-qr-${recipient.name}-${transfer.amount}.png`"
            block
          >
            ดาวน์โหลด QR
          </Button>
        </div>
      </div>
      <form
        v-else
        @submit="onSubmit"
      >
        <FormFields :options="formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            block
            color="warning"
            :disabled="!form.meta.value.dirty"
            type="submit"
          >
            สร้าง QR Code
          </Button>
        </div>
      </form>
    </template>
  </component>
</template>

<script lang="ts" setup>
import QRCode from 'qrcode'
import generatePayload from 'promptpay-qr'
import { useMediaQuery } from '@vueuse/core'
import type { Person, Transfer } from '~/types/bill-splitter'
import { Modal, Slideover } from '#components'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  recipient: Person
  promptPayId?: string
  transfer: Transfer
}>()

const qrCodeDataUrl = ref('')
const isDesktop = useMediaQuery('(min-width: 768px)')
const bill = useBillById()

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      promptPayId: v.optional(v.pipe(v.string(), v.nonEmpty(), v.minLength(10)), ''),
    }),
  ),
})

const newPromptPayId = ref(props.promptPayId || '')

onMounted(() => {
  if (props.promptPayId) {
    generateQrCodeInternal(props.promptPayId, props.transfer.amount)
  }
})

const generateQrCodeInternal = async (id: string, amount: number) => {
  const payload = generatePayload(id, {
    amount,
  })

  try {
    // Generate QR code as data URL
    const qrDataUrl = await QRCode.toDataURL(payload, {
      width: 256,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    })

    // Add logo to center of QR code
    qrCodeDataUrl.value = await addLogoToQRCode(qrDataUrl)
  } catch (err) {
    console.error('Failed to generate QR code', err)
    qrCodeDataUrl.value = await QRCode.toDataURL('Error: Could not generate QR code') // Show error in QR
  }
}

const addLogoToQRCode = async (qrDataUrl: string): Promise<string> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    const qrImage = new Image()

    qrImage.onload = () => {
      // Set canvas size to match QR code
      canvas.width = qrImage.width
      canvas.height = qrImage.height

      // Draw QR code
      ctx.drawImage(qrImage, 0, 0)

      // Load and draw logo
      const logo = new Image()

      logo.onload = () => {
        // Calculate logo size (about 20% of QR code size)
        const logoSize = Math.min(qrImage.width, qrImage.height) * 0.2
        const logoX = (qrImage.width - logoSize) / 2
        const logoY = (qrImage.height - logoSize) / 2

        // Draw logo
        ctx.drawImage(logo, logoX, logoY, logoSize, logoSize)

        // Convert canvas to data URL
        resolve(canvas.toDataURL('image/png'))
      }

      logo.onerror = () => {
        // If logo fails to load, return original QR code
        resolve(qrDataUrl)
      }

      // Set logo source - you can change this to any logo you want
      logo.src = '/promptpay-icon.png' // Make sure to add your logo file to public folder
    }

    qrImage.src = qrDataUrl
  })
}

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'promptPayId',
      label: 'รหัส PromptPay',
      placeholder: 'เช่น 08x-xxx-xxxx หรือ 1-xxxx-xxxxx-xx-x',
      required: true,
      autoFocus: true,
      mask: '#',
      maskTokens: {
        '#': {
          pattern: /[0-9\n]/,
          repeated: true,
        },
      },
    },
  },
])

const onSubmit = form.handleSubmit((values: any) => {
  newPromptPayId.value = values.promptPayId
  const personIndex = bill.item!.people.findIndex(
    (p) => p.name === props.recipient.name,
  )

  bill.updatePerson.update(personIndex, {
    ...bill.item!.people[personIndex],
    promptPayId: values.promptPayId,
  })

  generateQrCodeInternal(values.promptPayId, props.transfer.amount)
})
</script>
