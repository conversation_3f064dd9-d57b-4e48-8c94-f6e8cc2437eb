<template>
  <div class="min-h-screen">
    <div class="flex min-h-screen flex-col items-center justify-center">
      <div class="mb-8 text-center">
        <NuxtLink
          to="/"
          class="text-3xl font-bold text-white"
        >
          PaySync
        </NuxtLink>
        <p class="mt-2 text-sm text-white/80">
          แบ่งค่าอาหารอย่างยุติธรรม ไม่มีใครเสียเปรียบ
        </p>
      </div>
      <div class="w-full max-w-md rounded-2xl bg-white p-8 shadow-lg">
        <!-- Loading State -->
        <div
          v-if="isLoading"
          class="text-center"
        >
          <Loader />
          <p class="mt-4 text-sm text-gray-600">
            กำลังประมวลผลการเข้าสู่ระบบ...
          </p>
        </div>

        <!-- Error State -->
        <div
          v-else-if="error"
          class="text-center"
        >
          <div class="mb-4">
            <Icon
              name="heroicons:exclamation-triangle"
              class="mx-auto h-12 w-12 text-red-500"
            />
          </div>
          <h3 class="mb-2 text-lg font-semibold text-gray-900">
            เกิดข้อผิดพลาด
          </h3>
          <p class="mb-6 text-sm text-gray-600">
            {{ error }}
          </p>
          <Button
            color="primary"
            icon="material-symbols:refresh"
            @click="retryAuth"
          >
            ลองใหม่
          </Button>
        </div>

        <!-- Success State -->
        <div
          v-else-if="success"
          class="text-center"
        >
          <div class="mb-4">
            <Icon
              name="heroicons:check-circle"
              class="mx-auto h-12 w-12 text-green-500"
            />
          </div>
          <h3 class="mb-2 text-lg font-semibold text-gray-900">
            เข้าสู่ระบบสำเร็จ
          </h3>
          <p class="mb-6 text-sm text-gray-600">
            กำลังเปลี่ยนเส้นทางไปหน้าหลัก...
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Prevent this page from being indexed
definePageMeta({
  index: false,
})

const route = useRoute()
const auth = useAuth()

const isLoading = ref(true)
const error = ref<string | null>(null)
const success = ref(false)

// Handle LINE OAuth callback
const handleLineCallback = async () => {
  try {
    const {
      code,
      state,
      error: authError,
    } = route.query

    // Check for authentication errors
    if (authError) {
      throw new Error(`การเข้าสู่ระบบไม่สำเร็จ: ${authError}`)
    }

    // Validate required parameters
    if (!code || !state) {
      throw new Error('ข้อมูลการเข้าสู่ระบบไม่ครบถ้วน')
    }

    // // Verify state parameter
    // const storedState = sessionStorage.getItem('line_state')

    // if (state !== storedState) {
    //   throw new Error('สถานะการเข้าสู่ระบบไม่ถูกต้อง')
    // }

    // Exchange authorization code for access token
    const tokenResponse = await fetch('https://api.line.me/oauth2/v2.1/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code as string,
        redirect_uri: `${window.location.origin}/line-callback`,
        client_id: useRuntimeConfig().public.lineChannelId as string,
        client_secret: useRuntimeConfig().public.lineChannelSecret as string,
      }),
    })

    if (!tokenResponse.ok) {
      throw new Error('ไม่สามารถรับ access token ได้')
    }

    const tokenData = await tokenResponse.json()
    const {
      access_token, id_token,
    } = tokenData

    // Get user profile from LINE API
    const profileResponse = await fetch('https://api.line.me/v2/profile', {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    })

    if (!profileResponse.ok) {
      throw new Error('ไม่สามารถรับข้อมูลโปรไฟล์ได้')
    }

    const profile = await profileResponse.json()

    // Create client-side JWT token
    const {
      createLineToken,
    } = useLineAuth()

    const token = createLineToken(profile)

    // Store authentication token
    const authToken = useCookie('auth_token', {
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 days
    })

    authToken.value = token

    // Update user state
    auth.setUser({
      id: profile.userId,
      name: profile.displayName,
      avatar: profile.pictureUrl,
      provider: 'line',
      lineUserId: profile.userId,
    })

    // Sync session data
    await auth.syncUserSession(profile.userId)

    // Clean up session storage
    sessionStorage.removeItem('line_state')
    sessionStorage.removeItem('line_nonce')

    success.value = true

    // Redirect to home page after a short delay
    setTimeout(() => {
      navigateTo('/')
    }, 1500)
  } catch (err) {
    console.error('LINE authentication error:', err)
    error.value = err instanceof Error ? err.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'
  } finally {
    isLoading.value = false
  }
}

// Retry authentication
const retryAuth = () => {
  // Clear error and redirect to login
  error.value = null
  navigateTo('/')
}

// Handle the callback when component mounts
onMounted(() => {
  handleLineCallback()
})
</script>
