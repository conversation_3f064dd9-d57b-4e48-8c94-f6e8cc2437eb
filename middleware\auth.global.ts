export default defineNuxtRouteMiddleware(async (to) => {
  if (to.query.auth_token) {
    const token = to.query.auth_token as string
    const authTokenCookie = useCookie('auth_token', {
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 days
    })

    authTokenCookie.value = token

    // Handle session sync for LINE users
    if (to.query.sync_session) {
      // Let the auth plugin handle the sync after token is set
      if (import.meta.client) {
        sessionStorage.setItem('pending_sync', 'true')
      }
    }

    // Remove token and sync_session from URL
    return navigateTo(to.path, {
      replace: true,
    })
  }

  // Handle auth error from URL
  if (to.query.auth_error) {
    console.error('Authentication error:', to.query.auth_error)

    // Remove error from URL
    return navigateTo(to.path, {
      replace: true,
    })
  }
})
