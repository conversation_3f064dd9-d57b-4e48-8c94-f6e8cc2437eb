import { useMappedSessionsCache } from './useMappedSessionsCache'
import { useClientAuth } from './useClientAuth'
import { useLineAuth } from './useLineAuth'
import { getAvatarUrl } from '~/utils/avatar'

export interface User {
  id: string
  name: string
  avatar?: string
  provider: 'line' | 'guest'
  lineUserId?: string
}

export const useAuth = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  const isAuthenticating = ref(false)
  const isAuthenticated = computed(() => user.value !== null)
  const isGuest = computed(() => user.value?.provider === 'guest')
  const isLineUser = computed(() => user.value?.provider === 'line')

  // Check for existing auth state - decode JWT directly
  const checkAuth = async () => {
    isLoading.value = true

    try {
      const token = useCookie('auth_token', {
        path: '/',
        maxAge: 60 * 60 * 24 * 30, // 30 days
      })

      if (token.value) {
        try {
          // Handle guest tokens
          if (token.value.startsWith('guest_')) {
            const sessionId = token.value.replace('guest_', '')

            user.value = {
              id: sessionId,
              name: 'Guest User',
              provider: 'guest',
            }

            return true
          }

          // Decode JWT token directly for LINE users - simple base64 decode
          const payload = JSON.parse(atob(token.value.split('.')[1]))

          user.value = {
            id: payload.sub,
            name: payload.name,
            avatar: payload.picture,
            provider: 'line',
            lineUserId: payload.sub,
          }

          // Sync session for LINE users
          if (user.value.provider === 'line' && user.value.lineUserId) {
            isAuthenticating.value = true
            await syncUserSession(user.value.lineUserId)
            isAuthenticating.value = false
          }

          return true
        } catch (error) {
          // Token is invalid, clear it
          token.value = null
        }
      }

      return false
    } finally {
      isLoading.value = false
    }
  }

  // LINE Login (client-side)
  const loginWithLine = () => {
    const {
      initiateLineLogin,
    } = useLineAuth()

    initiateLineLogin()
  }

  // Sync session data when LINE user authenticates
  const syncUserSession = async (lineUserId: string) => {
    const sessionId = useCookie('sessionId', {
      path: '/',
      maxAge: 60 * 60 * 24 * 365,
    })

    if (sessionId.value) {
      try {
        // First, check if this LINE user already has existing mapped sessions (with caching)
        const mappedSessionsCache = useMappedSessionsCache()
        const existingMappings = await mappedSessionsCache.getMappedSessionData(lineUserId)

        if (existingMappings.sessionIds && existingMappings.sessionIds.length > 0) {
          // User already has mapped sessions, replace current session with the primary one
          const primarySessionId = existingMappings.sessionIds[0] // Use first mapped session as primary

          console.log(`Replacing session ${sessionId.value} with existing primary session ${primarySessionId}`)

          // Update the session cookie to use the existing primary session
          sessionId.value = primarySessionId

          // Update user object with stored data from mapping
          if (existingMappings.userData) {
            user.value = {
              id: primarySessionId,
              name: existingMappings.userData.displayName,
              avatar: existingMappings.userData.avatarUrl || undefined,
              provider: 'line',
              lineUserId,
            }
          }

          // Don't create a new mapping since this LINE user already exists
          return
        }

        // No existing mappings, create new mapping for current session
        const {
          createSessionMapping,
        } = useClientAuth()

        await createSessionMapping(
          sessionId.value,
          lineUserId,
          user.value?.name || 'LINE User',
          user.value?.avatar || null,
        )
      } catch (error) {
        console.error('Failed to sync session data:', error)
      }
    }
  }

  // Logout
  const logout = () => {
    const token = useCookie('auth_token')
    const sessionId = useCookie('sessionId')

    token.value = null
    sessionId.value = null
    user.value = null

    // Clear LINE session storage
    if (import.meta.client) {
      sessionStorage.removeItem('line_state')
      sessionStorage.removeItem('line_nonce')
    }

    // Redirect to home
    navigateTo('/', {
      external: true,
    })
  }

  // Get user display name
  const getDisplayName = () => {
    if (!user.value) return 'Anonymous'

    return user.value.name || 'User'
  }

  // Get user avatar
  const getAvatar = () => {
    if (user.value?.avatar) return user.value.avatar

    // Generate avatar for consistency
    return getAvatarUrl(user.value?.id || 'anonymous')
  }

  const setUser = (newUser: User | null) => {
    user.value = {
      ...newUser,
      name: base64ToUtf8(newUser?.name || ''),
    } as User | null
  }

  return {
    user,
    isLoading,
    isAuthenticating,
    setUser,
    isAuthenticated,
    isGuest,
    isLineUser,
    checkAuth,
    loginWithLine,
    logout,
    getDisplayName,
    getAvatar,
    syncUserSession,
  }
})
