<template>
  <div
    class="min-h-screen pb-[100px] lg:pb-0"
  >
    <div
      v-if="auth.isLoading"
      class="min-h-screen bg-gray-50"
    >
      <SkeletonLoader type="header" />
      <div class="mx-auto max-w-xl space-y-4 overflow-hidden px-4 py-2 lg:px-4 lg:py-4">
        <SkeletonLoader
          type="bill-list"
          :count="4"
        />
      </div>
    </div>

    <!-- Show main content when auth is loaded -->
    <div v-else>
      <AppHeader />
      <div class="mx-auto max-w-xl space-y-4 overflow-hidden px-4 py-2 lg:px-4 lg:py-4">
        <div v-if="billList.status.isLoading">
          <div class="mt-10 space-y-6">
            <Skeleton class="h-20 rounded" />
            <Skeleton class="h-20 rounded" />
            <Skeleton class="h-20 rounded" />
            <Skeleton class="h-20 rounded" />
          </div>
        </div>
        <Card
          v-else
          title="บิลทั้งหมด"
        >
          <HistoryList />
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const auth = useAuth()
const billList = useBillList()

onMounted(async () => {
  billList.fetch()
})
</script>
